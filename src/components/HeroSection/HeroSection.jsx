import React from 'react';
import styles from './HeroSection.module.scss';

/**
 * HeroSection Component
 *
 * A hero section that displays the main headline using animated text SVGs.
 * Based on Figma design: https://www.figma.com/design/5liAdEWw4jsBOXeMDtt73a/web-%E5%BC%80%E5%8F%91%E8%80%85%E7%BD%91%E7%AB%99---yy?node-id=1042-8153
 *
 * @returns {JSX.Element} The HeroSection component
 */
const HeroSection = () => {
  return (
    <div className={styles.heroSection}>
      {/* Background SVG */}
      <img
        src="/home-bg.svg"
        alt="Hero section background"
        className={styles.heroBg}
      />

      {/* Animated Text Elements */}
      <div className={styles.textContainer}>
        <img
          src="/Beyond.svg"
          alt="Beyond"
          className={`${styles.textElement} ${styles.beyond}`}
        />
        <img
          src="/Optimization.svg"
          alt="Optimization"
          className={`${styles.textElement} ${styles.optimization}`}
        />
        <img
          src="/WhereTech.svg"
          alt="Where Tech"
          className={`${styles.textElement} ${styles.whereTech}`}
        />
        <img
          src="/MeetsIntuition.svg"
          alt="Meets Intuition"
          className={`${styles.textElement} ${styles.meetsIntuition}`}
        />
      </div>
    </div>
  );
};

export default HeroSection;
