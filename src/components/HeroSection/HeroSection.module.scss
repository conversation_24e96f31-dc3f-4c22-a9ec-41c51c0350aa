.heroSection {
  width: 100%;
  height: 77.75rem; // 1244px / 16 = 77.75rem
  position: relative;
  margin-top: -8rem;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;

  @media (max-width: 768px) {
    margin-top: -4rem;
    height: 80vh; // Responsive height for mobile
    min-height: 37.5rem; // 600px / 16 = 37.5rem
  }
}

.heroBg {
  width: 100%;
  max-width: 1920px;
  height: auto;
  display: block;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;

  @media (max-width: 768px) {
    width: 100%;
    height: auto;
  }
}

.textContainer {
  position: relative;
  width: 100%;
  max-width: 1920px;
  height: 100%;
  z-index: 2;
}

.textElement {
  position: absolute;
  height: auto;
  z-index: 3;

  @media (max-width: 768px) {
    // Scale down text elements on mobile
    transform: scale(0.6);
    transform-origin: left top;
  }
}

// Text positioning based on 1920x1244 canvas, converted to rem
.beyond {
  top: 11.625rem; // 186px / 16 = 11.625rem
  left: 10.1875rem; // 163px / 16 = 10.1875rem
  width: auto;
}

.optimization {
  top: 27.4375rem; // 439px / 16 = 27.4375rem
  left: 10.1875rem; // 163px / 16 = 10.1875rem
  width: auto;
}

.whereTech {
  top: 43.25rem; // 692px / 16 = 43.25rem
  left: 10.1875rem; // 163px / 16 = 10.1875rem
  width: auto;
}

.meetsIntuition {
  top: 59.0625rem; // 945px / 16 = 59.0625rem
  left: 10.1875rem; // 163px / 16 = 10.1875rem
  width: auto;
}
