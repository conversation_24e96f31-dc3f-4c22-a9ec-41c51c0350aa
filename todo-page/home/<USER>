html {
  font-size: 16px;
}

.property1TopAfter {
  display: flex;
  align-items: flex-start;
  padding-bottom: 15.0625rem;
  padding-left: 10.1875rem;
  width: 109.8125rem;
  height: 62.6875rem;
  overflow: hidden;
  letter-spacing: 0;
  color: #000000;
  font-family: Moul;
  font-size: 8.75rem;
  background-image: linear-gradient(180deg, #def2ed 0%, #feffef 100%);

  .whereTech {
    color: #18725f;
  }

  .autoWrapper3 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: -11.75rem;

    .rectangle1430106780 {
      margin-top: -11.75rem;
      margin-left: 1.75rem;
      border-radius: 2.8125rem;
      background: #ffd200;
      width: 16.5rem;
      height: 16.5rem;
      rotate: 33deg;
    }

    .autoWrapper {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-top: 6.875rem;
      width: 64.5rem;
      color: #18725f;

      .beyond {
        display: flex;
        align-items: center;
        width: 39.3125rem;
        height: 11.75rem;
      }

      .frame2147229684 {
        margin-top: 1.0625rem;
        border: 0.375rem solid #18725f;
        border-radius: 1.75rem;
        background-color: #ffffff;
        width: 21.4375rem;
        height: 8.3125rem;
        overflow: hidden;
        background-image: url(https://p26-semi-sign.byteimg.com/tos-cn-i-acvclvrq33/2e2206a7741546de888aa16f874c8972.png?rk3s=521bdb00&x-expires=1753937694&x-signature=X3jNlJK44hZLO%2FAI1mkiqiYRuQw%3D);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
      }
    }

    .autoWrapper2 {
      position: relative;
      margin-top: 4.0625rem;
      width: 70.4375rem;
      height: 11.75rem;
      color: #262626;

      .frame {
        position: absolute;
        bottom: -2.0625rem;
        left: 34.9375rem;
        width: 33.1875rem;
        height: 3.125rem;
      }

      .optimization {
        display: flex;
        position: absolute;
        top: 0;
        left: 0;
        align-items: center;
        width: 70.4375rem;
        height: 11.75rem;
      }
    }

    .whereTech3 {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin: 4.0625rem 0rem 0rem;
      width: 61.1875rem;
      height: 11.75rem;

      .whereTech2 {
        color: #737af9;
      }
    }
  }

  .autoWrapper4 {
    position: relative;
    margin-top: 16.6875rem;
    margin-left: 8.125rem;
    width: 46rem;
    height: 46rem;

    .group12 {
      position: absolute;
      top: 0;
      left: 0;
      width: 46rem;
      height: 46rem;
    }

    .frame2 {
      position: absolute;
      bottom: -7rem;
      left: -85.3125rem;
      width: 38.5rem;
      height: 10.9375rem;
    }

    .meetsIntuition2 {
      display: flex;
      position: absolute;
      bottom: -8.125rem;
      left: -78.5625rem;
      flex-wrap: wrap;
      align-items: center;
      width: 79.4375rem;
      height: 11.75rem;

      .meetsIntuition {
        color: #262626;
      }
    }
  }
}
